[{"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\api\\[...path]\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\authApi.ts": "2", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\callback\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\DashboardCard.tsx": "5", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\QuickActionButton.tsx": "6", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\RecentActivity.tsx": "7", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\dashboardApi.ts": "8", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\hooks\\useDashboardData.ts": "9", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\layout.tsx": "10", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page.tsx": "11", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\layout.tsx": "12", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\categories\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\create.tsx": "14", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productCategoriesApi.ts": "16", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductForm.tsx": "17", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductModal.tsx": "18", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productsApi.ts": "19", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\[uuid]\\edit.tsx": "20", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\api.ts": "21", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\components\\ProductSelectionModal.tsx": "22", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\page.tsx": "23", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\alerts\\page.tsx": "24", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\levels\\page.tsx": "25", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\movements\\page.tsx": "26", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\transfers\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\vans\\page.tsx": "28", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\api.ts": "29", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\hooks\\useStockLevelsData.ts": "30", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\layout.tsx": "32", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\layout.tsx": "33", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\api.ts": "34", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\page.tsx": "35", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\RegionForm.tsx": "36", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\api.ts": "37", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\ComputeRouteModal.tsx": "38", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteList.tsx": "39", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteMap.tsx": "40", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-loading\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-stock\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\page.tsx": "44", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanForm.tsx": "45", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanModal.tsx": "46", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\vansApi.ts": "47", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\warehouses\\page.tsx": "48", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\page.tsx": "49", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\providers.tsx": "50", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\page.tsx": "51", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\goods-receipt\\page.tsx": "52", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\layout.tsx": "53", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\orders\\page.tsx": "54", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\returns\\page.tsx": "55", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierModal.tsx": "56", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierSearchBar.tsx": "57", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SuppliersTable.tsx": "58", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\page.tsx": "59", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\suppliersApi.ts": "60", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\types.ts": "61", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\financial\\page.tsx": "62", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\inventory\\page.tsx": "63", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\layout.tsx": "64", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\sales\\page.tsx": "65", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\van-performance\\page.tsx": "66", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\cash-register\\page.tsx": "67", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\index.ts": "68", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\InvoicePrint.tsx": "69", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\OrderPrint.tsx": "70", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\Pagination.tsx": "71", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\CustomerTableActions.tsx": "72", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\index.ts": "73", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\SearchAndFilters.tsx": "74", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerDetailsModal.tsx": "75", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerModal.tsx": "76", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customerPaymentsApi.ts": "77", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customersApi.ts": "78", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\index.ts": "79", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerActions.ts": "80", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerFilters.ts": "81", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\CustomerMap.tsx": "82", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\page.tsx": "83", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\page.tsx": "84", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentDetailsModal.tsx": "85", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentModal.tsx": "86", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\page.tsx": "87", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomerPayments.ts": "88", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomers.ts": "89", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\invoiceApi.ts": "90", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\invoices\\page.tsx": "91", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\layout.tsx": "92", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\orders\\page.tsx": "93", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\ordersApi.ts": "94", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\quotes\\page.tsx": "95", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\returns\\page.tsx": "96", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\index.ts": "97", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\ListComponent.tsx": "98", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSComponent.tsx": "99", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSView.tsx": "100", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SaleDetailsModal.tsx": "101", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesCancelModal.tsx": "102", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesFilters.tsx": "103", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesHeader.tsx": "104", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesListView.tsx": "105", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\index.ts": "106", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesActions.ts": "107", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesData.ts": "108", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesPOSState.ts": "109", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\page.tsx": "110", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\CategoryFilter.tsx": "111", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\index.ts": "112", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\NotesControls.tsx": "113", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\Pagination.tsx": "114", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\PaymentSelector.tsx": "115", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\ProductList.tsx": "116", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\QuantityModal.tsx": "117", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SalesCart.tsx": "118", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SearchBar.tsx": "119", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\TaxControls.tsx": "120", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\config\\posConfig.ts": "121", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\index.ts": "122", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\useKeyboardNavigation.ts": "123", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSOperations.ts": "124", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSProducts.ts": "125", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSState.ts": "126", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\posApi.ts": "127", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\styles\\posStyles.ts": "128", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\types\\index.ts": "129", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\utils\\posHelpers.ts": "130", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesApi.ts": "131", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesHelpers.ts": "132", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\accountSettingsApi.ts": "133", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\companiesApi.ts": "134", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\data\\page.tsx": "135", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\layout.tsx": "136", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\profile\\page.tsx": "137", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\page.tsx": "138", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\rolesApi.ts": "139", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\system\\page.tsx": "140", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\AddUserModal.tsx": "141", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\DeleteUserDialog.tsx": "142", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\EditUserModal.tsx": "143", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserDetailsModal.tsx": "144", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserTable.tsx": "145", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\page.tsx": "146", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\usersApi.ts": "147", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\warehousesApi.ts": "148", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\BackendStatusChecker.tsx": "149", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\api\\customerApi.ts": "150", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\CustomerModal.tsx": "151", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\hooks\\useCustomerData.ts": "152", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\index.ts": "153", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\styles\\customerModalStyles.ts": "154", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\types\\index.ts": "155", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\utils\\customerHelpers.ts": "156", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTable.tsx": "157", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableLogic.ts": "158", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableStyles.tsx": "159", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ErrorToast.tsx": "160", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\ItemsTable.tsx": "161", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\LoadingDemo.tsx": "162", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\TableActionButtons.tsx": "163", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\NetworkStatusChecker.tsx": "164", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ProtectedRoute.tsx": "165", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SecurityStatus.tsx": "166", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SideTaskBar\\SideTaskBar.tsx": "167", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\TopTaskBar\\TopTaskBar.tsx": "168", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\LogDetailsModal.tsx": "169", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\layout.tsx": "170", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\logsApi.ts": "171", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\page.tsx": "172", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\index.ts": "173", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\UserModal.tsx": "174"}, {"size": 12076, "mtime": 1753559550141, "results": "175", "hashOfConfig": "176"}, {"size": 6234, "mtime": 1753197132802, "results": "177", "hashOfConfig": "176"}, {"size": 17268, "mtime": 1753559813872, "results": "178", "hashOfConfig": "176"}, {"size": 16195, "mtime": 1753559619509, "results": "179", "hashOfConfig": "176"}, {"size": 2255, "mtime": 1753197132803, "results": "180", "hashOfConfig": "176"}, {"size": 1442, "mtime": 1753197132803, "results": "181", "hashOfConfig": "176"}, {"size": 5289, "mtime": 1753197132804, "results": "182", "hashOfConfig": "176"}, {"size": 3431, "mtime": 1753197132804, "results": "183", "hashOfConfig": "176"}, {"size": 5367, "mtime": 1753205023094, "results": "184", "hashOfConfig": "176"}, {"size": 515, "mtime": 1753197132805, "results": "185", "hashOfConfig": "176"}, {"size": 7081, "mtime": 1753197132805, "results": "186", "hashOfConfig": "176"}, {"size": 627, "mtime": 1753197132805, "results": "187", "hashOfConfig": "176"}, {"size": 12628, "mtime": 1753436167332, "results": "188", "hashOfConfig": "176"}, {"size": 3128, "mtime": 1753437290519, "results": "189", "hashOfConfig": "176"}, {"size": 21177, "mtime": 1753437289410, "results": "190", "hashOfConfig": "176"}, {"size": 4912, "mtime": 1753197132807, "results": "191", "hashOfConfig": "176"}, {"size": 29276, "mtime": 1753437289458, "results": "192", "hashOfConfig": "176"}, {"size": 1765, "mtime": 1753197132806, "results": "193", "hashOfConfig": "176"}, {"size": 11257, "mtime": 1753437290514, "results": "194", "hashOfConfig": "176"}, {"size": 4763, "mtime": 1753437291482, "results": "195", "hashOfConfig": "176"}, {"size": 4048, "mtime": 1753436167336, "results": "196", "hashOfConfig": "176"}, {"size": 14144, "mtime": 1753436167337, "results": "197", "hashOfConfig": "176"}, {"size": 17067, "mtime": 1753436167337, "results": "198", "hashOfConfig": "176"}, {"size": 265, "mtime": 1753197132809, "results": "199", "hashOfConfig": "176"}, {"size": 254, "mtime": 1753197132809, "results": "200", "hashOfConfig": "176"}, {"size": 261, "mtime": 1753197132810, "results": "201", "hashOfConfig": "176"}, {"size": 263, "mtime": 1753197132810, "results": "202", "hashOfConfig": "176"}, {"size": 246, "mtime": 1753197132810, "results": "203", "hashOfConfig": "176"}, {"size": 7448, "mtime": 1753436167333, "results": "204", "hashOfConfig": "176"}, {"size": 9735, "mtime": 1753436167334, "results": "205", "hashOfConfig": "176"}, {"size": 10114, "mtime": 1753436167335, "results": "206", "hashOfConfig": "176"}, {"size": 711, "mtime": 1753205970784, "results": "207", "hashOfConfig": "176"}, {"size": 627, "mtime": 1753197132811, "results": "208", "hashOfConfig": "176"}, {"size": 2396, "mtime": 1753197132811, "results": "209", "hashOfConfig": "176"}, {"size": 13981, "mtime": 1753197132812, "results": "210", "hashOfConfig": "176"}, {"size": 9041, "mtime": 1753197132811, "results": "211", "hashOfConfig": "176"}, {"size": 3636, "mtime": 1753197132812, "results": "212", "hashOfConfig": "176"}, {"size": 9653, "mtime": 1753197132812, "results": "213", "hashOfConfig": "176"}, {"size": 5407, "mtime": 1753197132813, "results": "214", "hashOfConfig": "176"}, {"size": 17173, "mtime": 1753197132813, "results": "215", "hashOfConfig": "176"}, {"size": 12871, "mtime": 1753197132813, "results": "216", "hashOfConfig": "176"}, {"size": 213, "mtime": 1753197132814, "results": "217", "hashOfConfig": "176"}, {"size": 205, "mtime": 1753197132814, "results": "218", "hashOfConfig": "176"}, {"size": 10247, "mtime": 1753197132815, "results": "219", "hashOfConfig": "176"}, {"size": 7985, "mtime": 1753197132815, "results": "220", "hashOfConfig": "176"}, {"size": 3666, "mtime": 1753197132815, "results": "221", "hashOfConfig": "176"}, {"size": 1953, "mtime": 1753197132815, "results": "222", "hashOfConfig": "176"}, {"size": 12119, "mtime": 1753205023060, "results": "223", "hashOfConfig": "176"}, {"size": 911, "mtime": 1753197132816, "results": "224", "hashOfConfig": "176"}, {"size": 2318, "mtime": 1753205023085, "results": "225", "hashOfConfig": "176"}, {"size": 3459, "mtime": 1753513462684, "results": "226", "hashOfConfig": "176"}, {"size": 258, "mtime": 1753197132822, "results": "227", "hashOfConfig": "176"}, {"size": 628, "mtime": 1753197132822, "results": "228", "hashOfConfig": "176"}, {"size": 504, "mtime": 1753197132822, "results": "229", "hashOfConfig": "176"}, {"size": 267, "mtime": 1753197132823, "results": "230", "hashOfConfig": "176"}, {"size": 8134, "mtime": 1753197132823, "results": "231", "hashOfConfig": "176"}, {"size": 1602, "mtime": 1753197132823, "results": "232", "hashOfConfig": "176"}, {"size": 1876, "mtime": 1753197132824, "results": "233", "hashOfConfig": "176"}, {"size": 8905, "mtime": 1753197132824, "results": "234", "hashOfConfig": "176"}, {"size": 1695, "mtime": 1753197132824, "results": "235", "hashOfConfig": "176"}, {"size": 945, "mtime": 1753197132824, "results": "236", "hashOfConfig": "176"}, {"size": 237, "mtime": 1753197132825, "results": "237", "hashOfConfig": "176"}, {"size": 237, "mtime": 1753197132825, "results": "238", "hashOfConfig": "176"}, {"size": 625, "mtime": 1753197132825, "results": "239", "hashOfConfig": "176"}, {"size": 231, "mtime": 1753197132825, "results": "240", "hashOfConfig": "176"}, {"size": 229, "mtime": 1753197132826, "results": "241", "hashOfConfig": "176"}, {"size": 2434, "mtime": 1753197406252, "results": "242", "hashOfConfig": "176"}, {"size": 43, "mtime": 1753197132827, "results": "243", "hashOfConfig": "176"}, {"size": 14454, "mtime": 1753436167338, "results": "244", "hashOfConfig": "176"}, {"size": 16492, "mtime": 1753197132827, "results": "245", "hashOfConfig": "176"}, {"size": 5926, "mtime": 1753197132827, "results": "246", "hashOfConfig": "176"}, {"size": 2234, "mtime": 1753197132828, "results": "247", "hashOfConfig": "176"}, {"size": 119, "mtime": 1753197132829, "results": "248", "hashOfConfig": "176"}, {"size": 9699, "mtime": 1753197132829, "results": "249", "hashOfConfig": "176"}, {"size": 13821, "mtime": 1753436167339, "results": "250", "hashOfConfig": "176"}, {"size": 17006, "mtime": 1753436167339, "results": "251", "hashOfConfig": "176"}, {"size": 14008, "mtime": 1753473999868, "results": "252", "hashOfConfig": "176"}, {"size": 12722, "mtime": 1753436167341, "results": "253", "hashOfConfig": "176"}, {"size": 119, "mtime": 1753197132830, "results": "254", "hashOfConfig": "176"}, {"size": 4522, "mtime": 1753197132830, "results": "255", "hashOfConfig": "176"}, {"size": 4625, "mtime": 1753197132831, "results": "256", "hashOfConfig": "176"}, {"size": 11622, "mtime": 1753436167341, "results": "257", "hashOfConfig": "176"}, {"size": 13038, "mtime": 1753436167342, "results": "258", "hashOfConfig": "176"}, {"size": 7315, "mtime": 1753436167343, "results": "259", "hashOfConfig": "176"}, {"size": 16383, "mtime": 1753436167343, "results": "260", "hashOfConfig": "176"}, {"size": 17660, "mtime": 1753197132833, "results": "261", "hashOfConfig": "176"}, {"size": 25184, "mtime": 1753436167344, "results": "262", "hashOfConfig": "176"}, {"size": 10457, "mtime": 1753197132834, "results": "263", "hashOfConfig": "176"}, {"size": 4980, "mtime": 1753197132834, "results": "264", "hashOfConfig": "176"}, {"size": 1279, "mtime": 1753197132835, "results": "265", "hashOfConfig": "176"}, {"size": 243, "mtime": 1753197132835, "results": "266", "hashOfConfig": "176"}, {"size": 623, "mtime": 1753197132835, "results": "267", "hashOfConfig": "176"}, {"size": 27508, "mtime": 1753474974515, "results": "268", "hashOfConfig": "176"}, {"size": 4388, "mtime": 1753473980126, "results": "269", "hashOfConfig": "176"}, {"size": 237, "mtime": 1753197132837, "results": "270", "hashOfConfig": "176"}, {"size": 221, "mtime": 1753197132837, "results": "271", "hashOfConfig": "176"}, {"size": 630, "mtime": 1753197132840, "results": "272", "hashOfConfig": "176"}, {"size": 2466, "mtime": 1753197406252, "results": "273", "hashOfConfig": "176"}, {"size": 803, "mtime": 1753197406253, "results": "274", "hashOfConfig": "176"}, {"size": 22992, "mtime": 1753615162570, "results": "275", "hashOfConfig": "176"}, {"size": 5880, "mtime": 1753197132838, "results": "276", "hashOfConfig": "176"}, {"size": 1655, "mtime": 1753197132838, "results": "277", "hashOfConfig": "176"}, {"size": 12948, "mtime": 1753197132839, "results": "278", "hashOfConfig": "176"}, {"size": 3487, "mtime": 1753197132839, "results": "279", "hashOfConfig": "176"}, {"size": 7927, "mtime": 1753336414238, "results": "280", "hashOfConfig": "176"}, {"size": 182, "mtime": 1753197406254, "results": "281", "hashOfConfig": "176"}, {"size": 6693, "mtime": 1753474429230, "results": "282", "hashOfConfig": "176"}, {"size": 2884, "mtime": 1753436167346, "results": "283", "hashOfConfig": "176"}, {"size": 4058, "mtime": 1753436167347, "results": "284", "hashOfConfig": "176"}, {"size": 7434, "mtime": 1753436167348, "results": "285", "hashOfConfig": "176"}, {"size": 3956, "mtime": 1753197406256, "results": "286", "hashOfConfig": "176"}, {"size": 423, "mtime": 1753197132844, "results": "287", "hashOfConfig": "176"}, {"size": 1733, "mtime": 1753197132842, "results": "288", "hashOfConfig": "176"}, {"size": 5085, "mtime": 1753197406256, "results": "289", "hashOfConfig": "176"}, {"size": 4813, "mtime": 1753335895133, "results": "290", "hashOfConfig": "176"}, {"size": 11111, "mtime": 1753436167348, "results": "291", "hashOfConfig": "176"}, {"size": 5677, "mtime": 1753197132843, "results": "292", "hashOfConfig": "176"}, {"size": 17437, "mtime": 1753615169507, "results": "293", "hashOfConfig": "176"}, {"size": 2813, "mtime": 1753436167350, "results": "294", "hashOfConfig": "176"}, {"size": 2710, "mtime": 1753197406258, "results": "295", "hashOfConfig": "176"}, {"size": 2938, "mtime": 1753197132845, "results": "296", "hashOfConfig": "176"}, {"size": 552, "mtime": 1753197132846, "results": "297", "hashOfConfig": "176"}, {"size": 2266, "mtime": 1753197132846, "results": "298", "hashOfConfig": "176"}, {"size": 8146, "mtime": 1753615139969, "results": "299", "hashOfConfig": "176"}, {"size": 11960, "mtime": 1753197406259, "results": "300", "hashOfConfig": "176"}, {"size": 19208, "mtime": 1753615105379, "results": "301", "hashOfConfig": "176"}, {"size": 19553, "mtime": 1753474531750, "results": "302", "hashOfConfig": "176"}, {"size": 7958, "mtime": 1753197132847, "results": "303", "hashOfConfig": "176"}, {"size": 6788, "mtime": 1753197406260, "results": "304", "hashOfConfig": "176"}, {"size": 7124, "mtime": 1753331631171, "results": "305", "hashOfConfig": "176"}, {"size": 9365, "mtime": 1753474959969, "results": "306", "hashOfConfig": "176"}, {"size": 1887, "mtime": 1753436167353, "results": "307", "hashOfConfig": "176"}, {"size": 3432, "mtime": 1753197132849, "results": "308", "hashOfConfig": "176"}, {"size": 2165, "mtime": 1753197132850, "results": "309", "hashOfConfig": "176"}, {"size": 229, "mtime": 1753197132850, "results": "310", "hashOfConfig": "176"}, {"size": 626, "mtime": 1753197132850, "results": "311", "hashOfConfig": "176"}, {"size": 34646, "mtime": 1753197132851, "results": "312", "hashOfConfig": "176"}, {"size": 9490, "mtime": 1753197132851, "results": "313", "hashOfConfig": "176"}, {"size": 3004, "mtime": 1753197132851, "results": "314", "hashOfConfig": "176"}, {"size": 229, "mtime": 1753197132852, "results": "315", "hashOfConfig": "176"}, {"size": 6132, "mtime": 1753197132852, "results": "316", "hashOfConfig": "176"}, {"size": 1430, "mtime": 1753197132852, "results": "317", "hashOfConfig": "176"}, {"size": 4365, "mtime": 1753197132853, "results": "318", "hashOfConfig": "176"}, {"size": 6114, "mtime": 1753197132853, "results": "319", "hashOfConfig": "176"}, {"size": 7407, "mtime": 1753197132853, "results": "320", "hashOfConfig": "176"}, {"size": 9567, "mtime": 1753197132853, "results": "321", "hashOfConfig": "176"}, {"size": 8420, "mtime": 1753197132854, "results": "322", "hashOfConfig": "176"}, {"size": 5033, "mtime": 1753205022871, "results": "323", "hashOfConfig": "176"}, {"size": 5847, "mtime": 1753197132854, "results": "324", "hashOfConfig": "176"}, {"size": 2615, "mtime": 1753436167355, "results": "325", "hashOfConfig": "176"}, {"size": 17541, "mtime": 1753436167354, "results": "326", "hashOfConfig": "176"}, {"size": 5831, "mtime": 1753197132855, "results": "327", "hashOfConfig": "176"}, {"size": 642, "mtime": 1753197132856, "results": "328", "hashOfConfig": "176"}, {"size": 2513, "mtime": 1753197132856, "results": "329", "hashOfConfig": "176"}, {"size": 1810, "mtime": 1753197406261, "results": "330", "hashOfConfig": "176"}, {"size": 2468, "mtime": 1753197132856, "results": "331", "hashOfConfig": "176"}, {"size": 10624, "mtime": 1753197132859, "results": "332", "hashOfConfig": "176"}, {"size": 3420, "mtime": 1753197132859, "results": "333", "hashOfConfig": "176"}, {"size": 5127, "mtime": 1753197132860, "results": "334", "hashOfConfig": "176"}, {"size": 454, "mtime": 1753197132857, "results": "335", "hashOfConfig": "176"}, {"size": 14634, "mtime": 1753467887894, "results": "336", "hashOfConfig": "176"}, {"size": 11861, "mtime": 1753197132860, "results": "337", "hashOfConfig": "176"}, {"size": 2680, "mtime": 1753197132861, "results": "338", "hashOfConfig": "176"}, {"size": 9245, "mtime": 1753197132857, "results": "339", "hashOfConfig": "176"}, {"size": 737, "mtime": 1753201780688, "results": "340", "hashOfConfig": "176"}, {"size": 5905, "mtime": 1753197132857, "results": "341", "hashOfConfig": "176"}, {"size": 14909, "mtime": 1753467889968, "results": "342", "hashOfConfig": "176"}, {"size": 10177, "mtime": 1753436167356, "results": "343", "hashOfConfig": "176"}, {"size": 12049, "mtime": 1753616508372, "results": "344", "hashOfConfig": "176"}, {"size": 519, "mtime": 1753467890059, "results": "345", "hashOfConfig": "176"}, {"size": 4107, "mtime": 1753612248529, "results": "346", "hashOfConfig": "176"}, {"size": 16587, "mtime": 1753612350118, "results": "347", "hashOfConfig": "176"}, {"size": 93, "mtime": 1753467890146, "results": "348", "hashOfConfig": "176"}, {"size": 11130, "mtime": 1753467890115, "results": "349", "hashOfConfig": "176"}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0kge6", {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\api\\[...path]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\authApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\callback\\page.tsx", ["872"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\page.tsx", [], ["873"], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\DashboardCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\QuickActionButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\dashboardApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\hooks\\useDashboardData.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page.tsx", ["874"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\categories\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\create.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\page.tsx", ["875"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productCategoriesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductForm.tsx", ["876", "877"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\[uuid]\\edit.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\components\\ProductSelectionModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\page.tsx", [], ["878", "879"], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\alerts\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\levels\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\movements\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\transfers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\vans\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\hooks\\useStockLevelsData.ts", ["880", "881"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\RegionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\ComputeRouteModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteMap.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\page.tsx", ["882"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-loading\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-stock\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\vansApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\warehouses\\page.tsx", ["883", "884", "885"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\goods-receipt\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\returns\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierSearchBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SuppliersTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\suppliersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\financial\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\inventory\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\van-performance\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\cash-register\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\InvoicePrint.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\OrderPrint.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\CustomerTableActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\SearchAndFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customerPaymentsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerFilters.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\CustomerMap.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\page.tsx", ["886", "887", "888", "889"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomerPayments.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\invoiceApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\invoices\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\orders\\page.tsx", ["890"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\ordersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\quotes\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\returns\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\ListComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSView.tsx", ["891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SaleDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesCancelModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesHeader.tsx", ["904"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesListView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesData.ts", ["905", "906", "907", "908"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesPOSState.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\CategoryFilter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\NotesControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\PaymentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\ProductList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\QuantityModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SalesCart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SearchBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\TaxControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\config\\posConfig.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\useKeyboardNavigation.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSOperations.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSProducts.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSState.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\posApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\styles\\posStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\utils\\posHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\accountSettingsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\companiesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\data\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\rolesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\system\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\AddUserModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\DeleteUserDialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\EditUserModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserDetailsModal.tsx", ["909"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\usersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\warehousesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\BackendStatusChecker.tsx", ["910"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\api\\customerApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\CustomerModal.tsx", ["911", "912", "913", "914"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\hooks\\useCustomerData.ts", ["915", "916"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\styles\\customerModalStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\utils\\customerHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableLogic.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableStyles.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ErrorToast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\ItemsTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\LoadingDemo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\TableActionButtons.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\NetworkStatusChecker.tsx", ["917"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SecurityStatus.tsx", ["918"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SideTaskBar\\SideTaskBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\TopTaskBar\\TopTaskBar.tsx", ["919"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\LogDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\logsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\UserModal.tsx", ["920", "921", "922"], [], {"ruleId": "923", "severity": 1, "message": "924", "line": 255, "column": 6, "nodeType": "925", "endLine": 255, "endColumn": 8, "suggestions": "926"}, {"ruleId": "923", "severity": 1, "message": "927", "line": 78, "column": 6, "nodeType": "925", "endLine": 78, "endColumn": 29, "suggestions": "928", "suppressions": "929"}, {"ruleId": "930", "severity": 2, "message": "931", "line": 58, "column": 50, "nodeType": "932", "messageId": "933", "suggestions": "934"}, {"ruleId": "923", "severity": 1, "message": "935", "line": 228, "column": 6, "nodeType": "925", "endLine": 228, "endColumn": 55, "suggestions": "936"}, {"ruleId": "923", "severity": 1, "message": "937", "line": 433, "column": 6, "nodeType": "925", "endLine": 433, "endColumn": 54, "suggestions": "938"}, {"ruleId": "923", "severity": 1, "message": "939", "line": 433, "column": 7, "nodeType": "940", "endLine": 433, "endColumn": 21}, {"ruleId": "923", "severity": 1, "message": "941", "line": 152, "column": 6, "nodeType": "925", "endLine": 152, "endColumn": 66, "suggestions": "942", "suppressions": "943"}, {"ruleId": "923", "severity": 1, "message": "944", "line": 152, "column": 20, "nodeType": "940", "endLine": 152, "endColumn": 65, "suppressions": "945"}, {"ruleId": "923", "severity": 1, "message": "946", "line": 57, "column": 48, "nodeType": "925", "endLine": 57, "endColumn": 62, "suggestions": "947"}, {"ruleId": "923", "severity": 1, "message": "948", "line": 58, "column": 56, "nodeType": "925", "endLine": 58, "endColumn": 74, "suggestions": "949"}, {"ruleId": "923", "severity": 1, "message": "950", "line": 48, "column": 6, "nodeType": "925", "endLine": 48, "endColumn": 21, "suggestions": "951"}, {"ruleId": "923", "severity": 1, "message": "952", "line": 185, "column": 6, "nodeType": "925", "endLine": 185, "endColumn": 16, "suggestions": "953"}, {"ruleId": "930", "severity": 2, "message": "954", "line": 348, "column": 42, "nodeType": "932", "messageId": "933", "suggestions": "955"}, {"ruleId": "930", "severity": 2, "message": "954", "line": 348, "column": 56, "nodeType": "932", "messageId": "933", "suggestions": "956"}, {"ruleId": "923", "severity": 1, "message": "957", "line": 400, "column": 6, "nodeType": "925", "endLine": 400, "endColumn": 8, "suggestions": "958"}, {"ruleId": "923", "severity": 1, "message": "959", "line": 447, "column": 6, "nodeType": "925", "endLine": 447, "endColumn": 39, "suggestions": "960"}, {"ruleId": "923", "severity": 1, "message": "959", "line": 469, "column": 6, "nodeType": "925", "endLine": 469, "endColumn": 39, "suggestions": "961"}, {"ruleId": "923", "severity": 1, "message": "959", "line": 483, "column": 6, "nodeType": "925", "endLine": 483, "endColumn": 29, "suggestions": "962"}, {"ruleId": "923", "severity": 1, "message": "963", "line": 76, "column": 6, "nodeType": "925", "endLine": 76, "endColumn": 42, "suggestions": "964"}, {"ruleId": "965", "severity": 2, "message": "966", "line": 207, "column": 24, "nodeType": "967", "endLine": 207, "endColumn": 35}, {"ruleId": "965", "severity": 2, "message": "968", "line": 271, "column": 3, "nodeType": "967", "endLine": 271, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "968", "line": 280, "column": 3, "nodeType": "967", "endLine": 280, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "968", "line": 285, "column": 3, "nodeType": "967", "endLine": 285, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "968", "line": 290, "column": 3, "nodeType": "967", "endLine": 290, "endColumn": 12}, {"ruleId": "923", "severity": 1, "message": "969", "line": 359, "column": 6, "nodeType": "925", "endLine": 359, "endColumn": 144, "suggestions": "970"}, {"ruleId": "965", "severity": 2, "message": "968", "line": 362, "column": 3, "nodeType": "967", "endLine": 362, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "968", "line": 399, "column": 3, "nodeType": "967", "endLine": 399, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "966", "line": 422, "column": 25, "nodeType": "967", "endLine": 422, "endColumn": 36}, {"ruleId": "965", "severity": 2, "message": "968", "line": 440, "column": 3, "nodeType": "967", "endLine": 440, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "968", "line": 452, "column": 3, "nodeType": "967", "endLine": 452, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "968", "line": 483, "column": 3, "nodeType": "967", "endLine": 483, "endColumn": 12}, {"ruleId": "965", "severity": 2, "message": "968", "line": 491, "column": 3, "nodeType": "967", "endLine": 491, "endColumn": 12}, {"ruleId": "923", "severity": 1, "message": "971", "line": 51, "column": 6, "nodeType": "925", "endLine": 51, "endColumn": 17, "suggestions": "972"}, {"ruleId": "923", "severity": 1, "message": "946", "line": 16, "column": 48, "nodeType": "925", "endLine": 16, "endColumn": 72, "suggestions": "973"}, {"ruleId": "923", "severity": 1, "message": "974", "line": 16, "column": 49, "nodeType": "940", "endLine": 16, "endColumn": 71}, {"ruleId": "923", "severity": 1, "message": "948", "line": 17, "column": 56, "nodeType": "925", "endLine": 17, "endColumn": 84, "suggestions": "975"}, {"ruleId": "923", "severity": 1, "message": "974", "line": 17, "column": 57, "nodeType": "940", "endLine": 17, "endColumn": 83}, {"ruleId": "976", "severity": 1, "message": "977", "line": 127, "column": 22, "nodeType": "978", "endLine": 131, "endColumn": 24}, {"ruleId": "923", "severity": 1, "message": "979", "line": 85, "column": 6, "nodeType": "925", "endLine": 85, "endColumn": 8, "suggestions": "980"}, {"ruleId": "923", "severity": 1, "message": "981", "line": 71, "column": 6, "nodeType": "925", "endLine": 71, "endColumn": 14, "suggestions": "982"}, {"ruleId": "923", "severity": 1, "message": "983", "line": 97, "column": 6, "nodeType": "925", "endLine": 97, "endColumn": 44, "suggestions": "984"}, {"ruleId": "930", "severity": 2, "message": "954", "line": 272, "column": 86, "nodeType": "932", "messageId": "933", "suggestions": "985"}, {"ruleId": "930", "severity": 2, "message": "954", "line": 272, "column": 99, "nodeType": "932", "messageId": "933", "suggestions": "986"}, {"ruleId": "923", "severity": 1, "message": "987", "line": 51, "column": 6, "nodeType": "925", "endLine": 51, "endColumn": 8, "suggestions": "988"}, {"ruleId": "923", "severity": 1, "message": "989", "line": 150, "column": 40, "nodeType": "967", "endLine": 150, "endColumn": 47}, {"ruleId": "923", "severity": 1, "message": "990", "line": 136, "column": 6, "nodeType": "925", "endLine": 136, "endColumn": 8, "suggestions": "991"}, {"ruleId": "923", "severity": 1, "message": "992", "line": 32, "column": 6, "nodeType": "925", "endLine": 32, "endColumn": 8, "suggestions": "993"}, {"ruleId": "923", "severity": 1, "message": "994", "line": 83, "column": 6, "nodeType": "925", "endLine": 83, "endColumn": 60, "suggestions": "995"}, {"ruleId": "923", "severity": 1, "message": "983", "line": 108, "column": 6, "nodeType": "925", "endLine": 108, "endColumn": 49, "suggestions": "996"}, {"ruleId": "930", "severity": 2, "message": "954", "line": 265, "column": 78, "nodeType": "932", "messageId": "933", "suggestions": "997"}, {"ruleId": "930", "severity": 2, "message": "954", "line": 265, "column": 91, "nodeType": "932", "messageId": "933", "suggestions": "998"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'clearAllTimeouts', 'handleAuth', and 'router'. Either include them or remove the dependency array.", "ArrayExpression", ["999"], "React Hook useEffect has missing dependencies: 'checkUserExists' and 'logout'. Either include them or remove the dependency array.", ["1000"], ["1001"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1002", "1003", "1004", "1005"], "React Hook React.useEffect has a missing dependency: 'previousPaginationData'. Either include it or remove the dependency array.", ["1006"], "React Hook React.useEffect has a missing dependency: 'watch'. Either include it or remove the dependency array.", ["1007"], "React Hook React.useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "React Hook useEffect has missing dependencies: 'adjustments' and 'setValue'. Either include them or remove the dependency array.", ["1008"], ["1009"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["1010"], "React Hook useMemo has a missing dependency: 'filter'. Either include it or remove the dependency array.", ["1011"], "React Hook useMemo has a missing dependency: 'pagination'. Either include it or remove the dependency array.", ["1012"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1013"], "React Hook useEffect has a missing dependency: 'loadWarehouses'. Either include it or remove the dependency array.", ["1014"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1015", "1016", "1017", "1018"], ["1019", "1020", "1021", "1022"], "React Hook useEffect has a missing dependency: 'handleAddPayment'. Either include it or remove the dependency array.", ["1023"], "React Hook useCallback has a missing dependency: 'actions'. Either include it or remove the dependency array.", ["1024"], ["1025"], ["1026"], "React Hook useEffect has a missing dependency: 'loadSales'. Either include it or remove the dependency array.", ["1027"], "react-hooks/rules-of-hooks", "React Hook \"useCallback\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "React Hook useEffect has a missing dependency: 'loadSale'. Either include it or remove the dependency array.", ["1028"], "React Hook useEffect has a missing dependency: 'handleNewSale'. Either include it or remove the dependency array.", ["1029"], ["1030"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["1031"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has missing dependencies: 'checkBackendStatus' and 'checkTimeout'. Either include them or remove the dependency array.", ["1032"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["1033"], "React Hook useEffect has a missing dependency: 'debouncedSearchTerm'. Either include it or remove the dependency array.", ["1034"], ["1035", "1036", "1037", "1038"], ["1039", "1040", "1041", "1042"], "React Hook useCallback has a missing dependency: 'CACHE_EXPIRY'. Either include it or remove the dependency array.", ["1043"], "The ref value 'requestTimeoutRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'requestTimeoutRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'checkNetworkStatus' and 'checkTimeout'. Either include them or remove the dependency array.", ["1044"], "React Hook useEffect has a missing dependency: 'fetchSecurityStatus'. Either include it or remove the dependency array.", ["1045"], "React Hook React.useEffect has missing dependencies: 'fetchAndPersistWarehouseInfo' and 'user'. Either include them or remove the dependency array.", ["1046"], ["1047"], ["1048", "1049", "1050", "1051"], ["1052", "1053", "1054", "1055"], {"desc": "1056", "fix": "1057"}, {"desc": "1058", "fix": "1059"}, {"kind": "1060", "justification": "1061"}, {"messageId": "1062", "data": "1063", "fix": "1064", "desc": "1065"}, {"messageId": "1062", "data": "1066", "fix": "1067", "desc": "1068"}, {"messageId": "1062", "data": "1069", "fix": "1070", "desc": "1071"}, {"messageId": "1062", "data": "1072", "fix": "1073", "desc": "1074"}, {"desc": "1075", "fix": "1076"}, {"desc": "1077", "fix": "1078"}, {"desc": "1079", "fix": "1080"}, {"kind": "1060", "justification": "1061"}, {"kind": "1060", "justification": "1061"}, {"desc": "1081", "fix": "1082"}, {"desc": "1083", "fix": "1084"}, {"desc": "1085", "fix": "1086"}, {"desc": "1087", "fix": "1088"}, {"messageId": "1062", "data": "1089", "fix": "1090", "desc": "1091"}, {"messageId": "1062", "data": "1092", "fix": "1093", "desc": "1094"}, {"messageId": "1062", "data": "1095", "fix": "1096", "desc": "1097"}, {"messageId": "1062", "data": "1098", "fix": "1099", "desc": "1100"}, {"messageId": "1062", "data": "1101", "fix": "1102", "desc": "1091"}, {"messageId": "1062", "data": "1103", "fix": "1104", "desc": "1094"}, {"messageId": "1062", "data": "1105", "fix": "1106", "desc": "1097"}, {"messageId": "1062", "data": "1107", "fix": "1108", "desc": "1100"}, {"desc": "1109", "fix": "1110"}, {"desc": "1111", "fix": "1112"}, {"desc": "1111", "fix": "1113"}, {"desc": "1114", "fix": "1115"}, {"desc": "1116", "fix": "1117"}, {"desc": "1118", "fix": "1119"}, {"desc": "1120", "fix": "1121"}, {"desc": "1081", "fix": "1122"}, {"desc": "1083", "fix": "1123"}, {"desc": "1124", "fix": "1125"}, {"desc": "1126", "fix": "1127"}, {"desc": "1128", "fix": "1129"}, {"messageId": "1062", "data": "1130", "fix": "1131", "desc": "1091"}, {"messageId": "1062", "data": "1132", "fix": "1133", "desc": "1094"}, {"messageId": "1062", "data": "1134", "fix": "1135", "desc": "1097"}, {"messageId": "1062", "data": "1136", "fix": "1137", "desc": "1100"}, {"messageId": "1062", "data": "1138", "fix": "1139", "desc": "1091"}, {"messageId": "1062", "data": "1140", "fix": "1141", "desc": "1094"}, {"messageId": "1062", "data": "1142", "fix": "1143", "desc": "1097"}, {"messageId": "1062", "data": "1144", "fix": "1145", "desc": "1100"}, {"desc": "1146", "fix": "1147"}, {"desc": "1148", "fix": "1149"}, {"desc": "1150", "fix": "1151"}, {"desc": "1152", "fix": "1153"}, {"desc": "1154", "fix": "1155"}, {"messageId": "1062", "data": "1156", "fix": "1157", "desc": "1091"}, {"messageId": "1062", "data": "1158", "fix": "1159", "desc": "1094"}, {"messageId": "1062", "data": "1160", "fix": "1161", "desc": "1097"}, {"messageId": "1062", "data": "1162", "fix": "1163", "desc": "1100"}, {"messageId": "1062", "data": "1164", "fix": "1165", "desc": "1091"}, {"messageId": "1062", "data": "1166", "fix": "1167", "desc": "1094"}, {"messageId": "1062", "data": "1168", "fix": "1169", "desc": "1097"}, {"messageId": "1062", "data": "1170", "fix": "1171", "desc": "1100"}, "Update the dependencies array to be: [clearAllTimeouts, handleAuth, router]", {"range": "1172", "text": "1173"}, "Update the dependencies array to be: [router, user, loading, checkUserExists, logout]", {"range": "1174", "text": "1175"}, "directive", "", "replaceWithAlt", {"alt": "1176"}, {"range": "1177", "text": "1178"}, "Replace with `&apos;`.", {"alt": "1179"}, {"range": "1180", "text": "1181"}, "Replace with `&lsquo;`.", {"alt": "1182"}, {"range": "1183", "text": "1184"}, "Replace with `&#39;`.", {"alt": "1185"}, {"range": "1186", "text": "1187"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [paginatedData, isLoading, currentPage, pageSize, previousPaginationData]", {"range": "1188", "text": "1189"}, "Update the dependencies array to be: [showAdditionalPrices, setValue, watch]", {"range": "1190", "text": "1191"}, "Update the dependencies array to be: [adjustments, setValue, storageUuid]", {"range": "1192", "text": "1193"}, "Update the dependencies array to be: [filter]", {"range": "1194", "text": "1195"}, "Update the dependencies array to be: [pagination]", {"range": "1196", "text": "1197"}, "Update the dependencies array to be: [loadInitialData, warehouseUuid]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [loadWarehouses, userUuid]", {"range": "1200", "text": "1201"}, {"alt": "1202"}, {"range": "1203", "text": "1204"}, "Replace with `&quot;`.", {"alt": "1205"}, {"range": "1206", "text": "1207"}, "Replace with `&ldquo;`.", {"alt": "1208"}, {"range": "1209", "text": "1210"}, "Replace with `&#34;`.", {"alt": "1211"}, {"range": "1212", "text": "1213"}, "Replace with `&rdquo;`.", {"alt": "1202"}, {"range": "1214", "text": "1215"}, {"alt": "1205"}, {"range": "1216", "text": "1217"}, {"alt": "1208"}, {"range": "1218", "text": "1219"}, {"alt": "1211"}, {"range": "1220", "text": "1221"}, "Update the dependencies array to be: [handleAddPayment]", {"range": "1222", "text": "1223"}, "Update the dependencies array to be: [actions, userUuid]", {"range": "1224", "text": "1225"}, {"range": "1226", "text": "1225"}, "Update the dependencies array to be: [actions]", {"range": "1227", "text": "1228"}, "Update the dependencies array to be: [warehouseUuid, currentPage, filter, loadSales]", {"range": "1229", "text": "1230"}, "Update the dependencies array to be: [editSaleData, loadSaleUuid, warehouseUuid, isEditMode, isLoadMode, saleState, loadSaleForEdit, loadSaleForContinue, setError, setLoading, loadSale]", {"range": "1231", "text": "1232"}, "Update the dependencies array to be: [handleNewSale, onNewSale]", {"range": "1233", "text": "1234"}, {"range": "1235", "text": "1195"}, {"range": "1236", "text": "1197"}, "Update the dependencies array to be: [checkBackendStatus, checkTimeout]", {"range": "1237", "text": "1238"}, "Update the dependencies array to be: [handleClose, isOpen]", {"range": "1239", "text": "1240"}, "Update the dependencies array to be: [isOpen, warehouseUuid, loadCustomers, debouncedSearchTerm]", {"range": "1241", "text": "1242"}, {"alt": "1202"}, {"range": "1243", "text": "1244"}, {"alt": "1205"}, {"range": "1245", "text": "1246"}, {"alt": "1208"}, {"range": "1247", "text": "1248"}, {"alt": "1211"}, {"range": "1249", "text": "1250"}, {"alt": "1202"}, {"range": "1251", "text": "1202"}, {"alt": "1205"}, {"range": "1252", "text": "1205"}, {"alt": "1208"}, {"range": "1253", "text": "1208"}, {"alt": "1211"}, {"range": "1254", "text": "1211"}, "Update the dependencies array to be: [CACHE_EXPIRY]", {"range": "1255", "text": "1256"}, "Update the dependencies array to be: [checkNetworkStatus, checkTimeout]", {"range": "1257", "text": "1258"}, "Update the dependencies array to be: [fetchSecurityStatus]", {"range": "1259", "text": "1260"}, "Update the dependencies array to be: [user?.uuid, user.warehouseUuid, user.warehouseName, user, fetchAndPersistWarehouseInfo]", {"range": "1261", "text": "1262"}, "Update the dependencies array to be: [isOpen, effectiveWarehouseUuid, loadUsers, debouncedSearchTerm]", {"range": "1263", "text": "1264"}, {"alt": "1202"}, {"range": "1265", "text": "1266"}, {"alt": "1205"}, {"range": "1267", "text": "1268"}, {"alt": "1208"}, {"range": "1269", "text": "1270"}, {"alt": "1211"}, {"range": "1271", "text": "1272"}, {"alt": "1202"}, {"range": "1273", "text": "1202"}, {"alt": "1205"}, {"range": "1274", "text": "1205"}, {"alt": "1208"}, {"range": "1275", "text": "1208"}, {"alt": "1211"}, {"range": "1276", "text": "1211"}, [10498, 10500], "[clearAllTimeouts, handleAuth, router]", [3802, 3825], "[router, user, loading, checkUserExists, logout]", "&apos;", [1745, 1774], "! Here&apos;s an overview of your ", "&lsquo;", [1745, 1774], "! Here&lsquo;s an overview of your ", "&#39;", [1745, 1774], "! Here&#39;s an overview of your ", "&rsquo;", [1745, 1774], "! Here&rsquo;s an overview of your ", [9126, 9175], "[paginatedData, isLoading, currentPage, pageSize, previousPaginationData]", [17880, 17928], "[showAdditionalPrices, setValue, watch]", [5237, 5297], "[adjustments, setValue, storageUuid]", [1716, 1730], "[filter]", [1789, 1807], "[pagination]", [1692, 1707], "[loadInitialData, warehouseUuid]", [6787, 6797], "[loadWarehouses, userUuid]", "&quot;", [11603, 11705], "\n              No warehouses found. Click &quot;Add Warehouse\" to create your first warehouse.\n            ", "&ldquo;", [11603, 11705], "\n              No warehouses found. Click &ldquo;Add Warehouse\" to create your first warehouse.\n            ", "&#34;", [11603, 11705], "\n              No warehouses found. Click &#34;Add Warehouse\" to create your first warehouse.\n            ", "&rdquo;", [11603, 11705], "\n              No warehouses found. Click &rdquo;Add Warehouse\" to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&quot; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&ldquo; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&#34; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&rdquo; to create your first warehouse.\n            ", [13806, 13808], "[handleAddPayment]", [15158, 15191], "[actions, userUuid]", [15855, 15888], [16405, 16428], "[actions]", [2288, 2324], "[warehouseUuid, currentPage, filter, loadSales]", [12933, 13071], "[editSaleData, loadSaleUuid, warehouseUuid, isEditMode, isLoadMode, saleState, loadSaleForEdit, loadSaleForContinue, setError, setLoading, loadSale]", [1358, 1369], "[handleNewSale, onNewSale]", [705, 729], [788, 816], [2758, 2760], "[checkBackendStatus, checkTimeout]", [2182, 2190], "[handleClose, isOpen]", [2880, 2918], "[isOpen, warehouseUuid, loadCustomers, debouncedSearchTerm]", [9605, 9634], "No customers found matching &quot;", [9605, 9634], "No customers found matching &ldquo;", [9605, 9634], "No customers found matching &#34;", [9605, 9634], "No customers found matching &rdquo;", [9646, 9647], [9646, 9647], [9646, 9647], [9646, 9647], [2007, 2009], "[CACHE_EXPIRY]", [4713, 4715], "[checkNetworkStatus, checkTimeout]", [1025, 1027], "[fetchSecurityStatus]", [2945, 2999], "[user?.uuid, user.warehouseUuid, user.warehouseName, user, fetchAndPersistWarehouseInfo]", [3576, 3619], "[isOpen, effectiveWarehouseUuid, loadUsers, debouncedSearchTerm]", [9420, 9445], "No users found matching &quot;", [9420, 9445], "No users found matching &ldquo;", [9420, 9445], "No users found matching &#34;", [9420, 9445], "No users found matching &rdquo;", [9457, 9458], [9457, 9458], [9457, 9458], [9457, 9458]]