globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/sales/sales/page.tsx":{"*":{"id":"(ssr)/./app/sales/sales/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/SideTaskBar/SideTaskBar.tsx":{"*":{"id":"(ssr)/./components/SideTaskBar/SideTaskBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/TopTaskBar/TopTaskBar.tsx":{"*":{"id":"(ssr)/./components/TopTaskBar/TopTaskBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/providers.tsx":{"*":{"id":"(ssr)/./app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/page.tsx":{"*":{"id":"(ssr)/./app/auth/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/logs/page.tsx":{"*":{"id":"(ssr)/./app/logs/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\page.tsx":{"id":"(app-pages-browser)/./app/sales/sales/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SideTaskBar\\SideTaskBar.tsx":{"id":"(app-pages-browser)/./components/SideTaskBar/SideTaskBar.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\TopTaskBar\\TopTaskBar.tsx":{"id":"(app-pages-browser)/./components/TopTaskBar/TopTaskBar.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\providers.tsx":{"id":"(app-pages-browser)/./app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\sonner\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\page.tsx":{"id":"(app-pages-browser)/./app/auth/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\page.tsx":{"id":"(app-pages-browser)/./app/logs/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\":[],"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page":[],"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\layout":["static/css/app/dashboard/layout.css"]}}