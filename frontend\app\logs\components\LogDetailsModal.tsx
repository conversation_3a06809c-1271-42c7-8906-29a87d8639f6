import React, { useEffect } from 'react';
import { FiX, FiUser, FiCalendar, FiTag, FiFileText, FiDatabase } from 'react-icons/fi';
import type { Log } from '../logsApi';

export interface LogDetailsModalProps {
  isOpen: boolean;
  log: Log | null;
  onClose: () => void;
  userName?: string;
}

export function LogDetailsModal({ isOpen, log, onClose, userName }: LogDetailsModalProps) {
  // Handle escape key to close modal
  useEffect(() => {
    if (!isOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Handle clicking outside modal to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Format date for display
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // Get operation color based on operation type
  const getOperationColor = (operation: string) => {
    const op = operation.toLowerCase();
    if (op.includes('create') || op.includes('add')) {
      return 'bg-green-100 text-green-800 border-green-200';
    } else if (op.includes('update') || op.includes('edit') || op.includes('modify')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    } else if (op.includes('delete') || op.includes('remove')) {
      return 'bg-red-100 text-red-800 border-red-200';
    } else if (op.includes('cancel') || op.includes('reject')) {
      return 'bg-orange-100 text-orange-800 border-orange-200';
    } else if (op.includes('approve') || op.includes('complete')) {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    } else if (op.includes('transfer') || op.includes('move')) {
      return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    } else if (op.includes('adjust') || op.includes('modify')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    } else {
      return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format JSON data for display
  const formatJsonData = (data: Record<string, any>) => {
    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      return 'Invalid JSON data';
    }
  };

  // Check if the log data contains delta changes
  const hasDeltaChanges = (data: Record<string, any>) => {
    return data && data.changes && typeof data.changes === 'object';
  };

  // Render delta changes in a user-friendly format
  const renderDeltaChanges = (changes: Record<string, { before: any; after: any }>) => {
    const changeEntries = Object.entries(changes);

    if (changeEntries.length === 0) {
      return (
        <div className="text-center py-4">
          <p className="text-gray-500 text-sm">No changes detected</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {changeEntries.map(([field, change]) => (
          <div key={field} className="border border-gray-200 rounded-lg p-4">
            <h5 className="font-medium text-gray-900 mb-3 capitalize">
              {field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </h5>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Before value */}
              <div>
                <label className="block text-xs font-medium text-red-700 mb-1">
                  Before
                </label>
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <pre className="text-sm text-red-800 whitespace-pre-wrap overflow-x-auto">
                    {change.before === null ? 'null' :
                     typeof change.before === 'object' ?
                     JSON.stringify(change.before, null, 2) :
                     String(change.before)}
                  </pre>
                </div>
              </div>

              {/* After value */}
              <div>
                <label className="block text-xs font-medium text-green-700 mb-1">
                  After
                </label>
                <div className="bg-green-50 border border-green-200 rounded p-3">
                  <pre className="text-sm text-green-800 whitespace-pre-wrap overflow-x-auto">
                    {change.after === null ? 'null' :
                     typeof change.after === 'object' ?
                     JSON.stringify(change.after, null, 2) :
                     String(change.after)}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (!isOpen || !log) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={handleOverlayClick}>
      <div className="bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900">
              Log Details
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              View detailed information about this log entry
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            title="Close"
            aria-label="Close modal"
          >
            <FiX className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Basic Information - Compact Layout */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Operation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiTag className="w-4 h-4" />
                  Operation
                </label>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getOperationColor(log.operation)}`}>
                  {log.operation}
                </span>
              </div>

              {/* Entity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiDatabase className="w-4 h-4" />
                  Entity
                </label>
                <span className="text-gray-900 font-medium">{log.entity}</span>
              </div>

              {/* User */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiUser className="w-4 h-4" />
                  User
                </label>
                <span className="text-gray-900 font-medium">
                  {userName || 'Unknown User'}
                </span>
                <p className="text-xs text-gray-500 mt-1">{log.userUuid}</p>
              </div>

              {/* Timestamp */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiCalendar className="w-4 h-4" />
                  Timestamp
                </label>
                <span className="text-gray-900">{formatDate(log.createdAt)}</span>
              </div>
            </div>

            {/* Description - Full Width */}
            {log.description && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiFileText className="w-4 h-4" />
                  Description
                </label>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-lg border">
                  {log.description}
                </p>
              </div>
            )}
          </div>

          {/* Data Section - Full Width */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {log.data && hasDeltaChanges(log.data) ? 'Changes' : 'Additional Data'}
            </h4>

            {log.data ? (
              <div>
                {hasDeltaChanges(log.data) ? (
                  <div>
                    {/* Delta Changes */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Field Changes ({log.data.changeCount || 0} changes)
                      </label>
                      {renderDeltaChanges(log.data.changes)}
                    </div>

                    {/* Additional metadata */}
                    {Object.keys(log.data).some(key => !['changes', 'changedFields', 'changeCount'].includes(key)) && (
                      <div className="mt-6">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Additional Information
                        </label>
                        <div className="bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto">
                          <pre className="text-sm font-mono whitespace-pre-wrap">
                            {formatJsonData(
                              Object.fromEntries(
                                Object.entries(log.data).filter(([key]) =>
                                  !['changes', 'changedFields', 'changeCount'].includes(key)
                                )
                              )
                            )}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      JSON Data
                    </label>
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto">
                      <pre className="text-sm font-mono whitespace-pre-wrap">
                        {formatJsonData(log.data)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiDatabase className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">No additional data available</p>
              </div>
            )}
          </div>

          {/* Log ID */}
          <div className="pt-6 border-t border-gray-200">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Log ID
            </label>
            <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
              {log.id}
            </code>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex-shrink-0 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
} 