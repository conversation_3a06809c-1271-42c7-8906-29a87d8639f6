import { Injectable, Logger } from '@nestjs/common';
import { LogsService } from './logs.service';
import { CreateLogDto } from './dto/create-log.dto';

@Injectable()
export class LogsUtilityService {
  private readonly logger = new Logger(LogsUtilityService.name);

  constructor(private readonly logsService: LogsService) {}

  /**
   * Log a user action with the specified operation and entity
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed (e.g., 'created', 'updated', 'deleted', 'cancelled')
   * @param entity - The entity being affected (e.g., 'sale123', 'product456', 'customer789')
   */
  async logAction(userUuid: string, operation: string, entity: string): Promise<void> {
    try {
      const createLogDto: CreateLogDto = {
        userUuid,
        operation,
        entity,
      };

      await this.logsService.create(createLogDto);
      this.logger.debug(`Logged action: ${operation} on ${entity} by user ${userUuid}`);
    } catch (error) {
      this.logger.error(`Failed to log action: ${operation} on ${entity} by user ${userUuid}`, error);
      // Don't throw the error to avoid breaking the main operation
    }
  }

  /**
   * Log a sale cancellation
   * @param userUuid - The UUID of the user cancelling the sale
   * @param saleId - The ID of the sale being cancelled
   */
  async logSaleCancellation(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'cancelled', `sale${saleId}`);
  }

  /**
   * Log a sale edit
   * @param userUuid - The UUID of the user editing the sale
   * @param saleId - The ID of the sale being edited
   */
  async logSaleEdit(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'edited', `sale${saleId}`);
  }

  /**
   * Log a sale creation
   * @param userUuid - The UUID of the user creating the sale
   * @param saleId - The ID of the sale being created
   */
  async logSaleCreation(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'created', `sale${saleId}`);
  }

  /**
   * Log a sale deletion
   * @param userUuid - The UUID of the user deleting the sale
   * @param saleId - The ID of the sale being deleted
   */
  async logSaleDeletion(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'deleted', `sale${saleId}`);
  }

  /**
   * Log a product action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param productId - The ID of the product being affected
   */
  async logProductAction(userUuid: string, operation: string, productId: string): Promise<void> {
    await this.logAction(userUuid, operation, `product${productId}`);
  }

  /**
   * Log a customer action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param customerId - The ID of the customer being affected
   */
  async logCustomerAction(userUuid: string, operation: string, customerId: string): Promise<void> {
    await this.logAction(userUuid, operation, `customer${customerId}`);
  }

  /**
   * Log an inventory action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param inventoryId - The ID of the inventory item being affected
   */
  async logInventoryAction(userUuid: string, operation: string, inventoryId: string): Promise<void> {
    await this.logAction(userUuid, operation, `inventory${inventoryId}`);
  }

  /**
   * Log a purchase action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param purchaseId - The ID of the purchase being affected
   */
  async logPurchaseAction(userUuid: string, operation: string, purchaseId: string): Promise<void> {
    await this.logAction(userUuid, operation, `purchase${purchaseId}`);
  }

  /**
   * Log a warehouse action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param warehouseId - The ID of the warehouse being affected
   */
  async logWarehouseAction(userUuid: string, operation: string, warehouseId: string): Promise<void> {
    await this.logAction(userUuid, operation, `warehouse${warehouseId}`);
  }

  /**
   * Log a van action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param vanId - The ID of the van being affected
   */
  async logVanAction(userUuid: string, operation: string, vanId: string): Promise<void> {
    await this.logAction(userUuid, operation, `van${vanId}`);
  }

  /**
   * Log a supplier action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param supplierId - The ID of the supplier being affected
   */
  async logSupplierAction(userUuid: string, operation: string, supplierId: string): Promise<void> {
    await this.logAction(userUuid, operation, `supplier${supplierId}`);
  }

  /**
   * Log a user action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param targetUserId - The ID of the user being affected
   */
  async logUserAction(userUuid: string, operation: string, targetUserId: string): Promise<void> {
    await this.logAction(userUuid, operation, `user${targetUserId}`);
  }
} 