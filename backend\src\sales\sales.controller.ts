import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  ParseU<PERSON><PERSON>ipe,
  BadRequestException,
} from "@nestjs/common";
import { CreateSaleDto } from "./dto/create-sale.dto";
import { UpdateSaleDto } from "./dto/update-sale.dto";
import { CancelSaleDto } from "./dto/cancel-sale.dto";
import { UpdateSaleStatusDto } from "./dto/update-sale-status.dto";
import { DeleteSaleDto } from "./dto/delete-sale.dto";
import { AddProductsToSaleDto } from "./dto/add-products-to-sale.dto";
import {
  EMPTY_STRING_FILTER,
  EMPTY_UUID_FILTER,
  MIN_DATE_FILTER,
  MAX_DATE_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./sales.constants";
import { SalesService } from "./sales.service.typeorm";
import { Sale, SaleStatus } from "./sale.entity";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import {
  SaleResponseDto,
  SalesListResponseDto,
} from "./dto/sales-response.dto";
import { Uuid7 } from "../utils/uuid7";
import { LogsService } from "../logs/logs.service";
import { In } from "typeorm";
// Utility function for debugging delay
const debugDelay = (ms: number = 3000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

@ApiTags("sales")
@Controller("sales")
export class SalesController {
  constructor(
    private readonly salesService: SalesService,
    private readonly logsService: LogsService,
  ) {}

  /**
   * Utility method to get entity names for logging
   */
  private async getEntityNames(data: {
    customerUuid?: string;
    warehouseUuid?: string;
    userUuid?: string;
    productUuids?: string[];
  }) {
    const entityNames: any = {};
    
    try {
      // Get customer name
      if (data.customerUuid) {
        const customer = await this.salesService["customerRepository"].findOne({
          where: { id: data.customerUuid, isDeleted: false },
        });
        entityNames.customerName = customer?.name || "Unknown Customer";
      }

      // Get warehouse name
      if (data.warehouseUuid) {
        const warehouse = await this.salesService["warehouseRepository"]?.findOne({
          where: { id: data.warehouseUuid, isDeleted: false },
        });
        entityNames.warehouseName = warehouse?.name || "Unknown Warehouse";
      }

      // Get user name
      if (data.userUuid) {
        try {
          const user = await this.salesService["usersService"].findOne(data.userUuid);
          entityNames.userName = user?.name || "Unknown User";
        } catch (error) {
          entityNames.userName = "Unknown User";
        }
      }

      // Get product names
      if (data.productUuids && data.productUuids.length > 0) {
        const products = await this.salesService["productRepository"].find({
          where: { id: In(data.productUuids), isDeleted: false },
        });
        entityNames.productNames = products.reduce((acc, product) => {
          acc[product.id] = product.name;
          return acc;
        }, {} as Record<string, string>);
      }
    } catch (error) {
      console.error("[Sales Controller] Error getting entity names:", error);
    }

    return entityNames;
  }

  @Post("/debug")
  @ApiOperation({ summary: "Debug sale creation prerequisites" })
  @ApiResponse({ status: 200, description: "Debug information" })
  async debugSaleCreation(@Body() createSaleDto: CreateSaleDto): Promise<any> {
    // Add 5-second delay for debugging
    await debugDelay();
    
    console.log(
      "[Sales Controller] Debug sale creation request:",
      createSaleDto,
    );
    console.log("[Sales Controller] Debug sale creation request types:", {
      userUuid: typeof createSaleDto.userUuid,
      warehouseUuid: typeof createSaleDto.warehouseUuid,
      customerUuid: typeof createSaleDto.customerUuid,
      itemsCount: createSaleDto.items?.length || 0,
      useTax: typeof createSaleDto.useTax,
      taxRate: typeof createSaleDto.taxRate,
    });

    const result = {
      userUuid: createSaleDto.userUuid,
      warehouseUuid: createSaleDto.warehouseUuid,
      customerUuid: createSaleDto.customerUuid,
      itemsCount: createSaleDto.items?.length || 0,
      userExists: false,
      warehouseExists: false,
      customerExists: false,
      validationErrors: [],
      errors: [],
    };

    // Validate basic structure
    if (!createSaleDto.items || createSaleDto.items.length === 0) {
      result.validationErrors.push("Sales must contain at least one item");
    }

    // Check user exists
    try {
      const user = await this.salesService["usersService"].findOne(
        createSaleDto.userUuid,
      );
      result.userExists = !!user;
      console.log("[Sales Controller] User check result:", result.userExists);
    } catch (error) {
      result.errors.push(`User check failed: ${error.message}`);
      console.error("[Sales Controller] User check error:", error);
    }

    // Check warehouse exists
    try {
      const warehouse = await this.salesService["warehouseRepository"].findOne({
        where: { id: createSaleDto.warehouseUuid, isDeleted: false },
      });
      result.warehouseExists = !!warehouse;
      console.log(
        "[Sales Controller] Warehouse check result:",
        result.warehouseExists,
      );
    } catch (error) {
      result.errors.push(`Warehouse check failed: ${error.message}`);
      console.error("[Sales Controller] Warehouse check error:", error);
    }

    // Check customer exists
    try {
      const customer = await this.salesService["customerRepository"].findOne({
        where: { id: createSaleDto.customerUuid, isDeleted: false },
      });
      result.customerExists = !!customer;
      console.log(
        "[Sales Controller] Customer check result:",
        result.customerExists,
      );
    } catch (error) {
      result.errors.push(`Customer check failed: ${error.message}`);
      console.error("[Sales Controller] Customer check error:", error);
    }

    return result;
  }

  @Post()
  @ApiOperation({ summary: "Create a new sale" })
  @ApiResponse({
    status: 201,
    description: "Sale created successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: "Invalid input data - detailed validation errors for missing fields, invalid items, or empty sales" 
  })
  @ApiResponse({ 
    status: 404, 
    description: "User, warehouse, customer, or product not found" 
  })
  @ApiResponse({ 
    status: 400, 
    description: "Invalid payment amount - cannot overpay" 
  })
  @ApiResponse({ 
    status: 400, 
    description: "Invalid userUuid" 
  })
  async create(@Body() createSaleDto: CreateSaleDto): Promise<SaleResponseDto> {
    // Add 5-second delay for debugging
    await debugDelay();
    
    console.log("[Sales Controller] Create sale request:", createSaleDto);
    console.log("[Sales Controller] User UUID:", createSaleDto.userUuid);

    // Validate payment amount if provided
    if (createSaleDto.amountPaid !== undefined && createSaleDto.items && createSaleDto.items.length > 0) {
      // Calculate total amount
      const subtotal = createSaleDto.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
      const taxAmount = createSaleDto.useTax ? subtotal * (createSaleDto.taxRate || 0.1) : 0;
      const totalAmount = subtotal + taxAmount;
      
      if (createSaleDto.amountPaid > totalAmount) {
        throw new BadRequestException(
          `Payment amount (${createSaleDto.amountPaid}) cannot exceed total sale amount (${totalAmount}). Overpayment is not allowed.`
        );
      }
    }

    try {
      const sale = await this.salesService.create(
        createSaleDto.userUuid,
        createSaleDto.warehouseUuid,
        createSaleDto.customerUuid,
        createSaleDto.items,
        createSaleDto.useTax,
        createSaleDto.taxRate,
        createSaleDto.status,
        createSaleDto.paymentMethod,
        createSaleDto.amountPaid,
      );

      console.log("[Sales Controller] Sale created successfully:", sale.uuid);

      // Get entity names for logging
      const entityNames = await this.getEntityNames({
        customerUuid: createSaleDto.customerUuid,
        warehouseUuid: createSaleDto.warehouseUuid,
        userUuid: createSaleDto.userUuid,
        productUuids: createSaleDto.items?.map(item => item.productUuid) || [],
      });

      // Log the sale creation
      await this.logsService.create({
        userUuid: createSaleDto.userUuid,
        operation: "created",
        entity: `sale_${sale.uuid}`,
        description: `Sale created with ${createSaleDto.items?.length || 0} items`,
        data: {
          saleUuid: sale.uuid,
          warehouseUuid: createSaleDto.warehouseUuid,
          warehouseName: entityNames.warehouseName,
          customerUuid: createSaleDto.customerUuid,
          customerName: entityNames.customerName,
          userName: entityNames.userName,
          itemsCount: createSaleDto.items?.length || 0,
          items: createSaleDto.items?.map(item => ({
            productUuid: item.productUuid,
            productName: entityNames.productNames?.[item.productUuid] || item.name,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            lineTotal: item.totalPrice || item.quantity * item.unitPrice,
            taxAmount: 0, // CreateSaleDto doesn't have taxAmount per item
          })) || [],
          subtotal: createSaleDto.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0,
          taxRate: createSaleDto.taxRate,
          useTax: createSaleDto.useTax,
          taxAmount: createSaleDto.useTax ? (createSaleDto.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0) * (createSaleDto.taxRate || 0.1) : 0,
          totalAmount: createSaleDto.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0 + (createSaleDto.useTax ? (createSaleDto.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0) * (createSaleDto.taxRate || 0.1) : 0),
          status: createSaleDto.status,
          paymentMethod: createSaleDto.paymentMethod,
          amountPaid: createSaleDto.amountPaid,
        },
      });

      // Convert the sale document to SaleResponseDto
      return this.salesService.findOne(sale.uuid);
    } catch (error) {
      console.error("[Sales Controller] Error creating sale:", error);
      throw error;
    }
  }



  @Patch(":uuid/cancel")
  @ApiOperation({ summary: "Cancel a sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale cancelled successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid userUuid" })
  async cancelSale(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() cancelSaleDto: CancelSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    console.log("[Sales Controller] Cancel sale request:", { uuid, userUuid: cancelSaleDto.userUuid });
    
    const result = await this.salesService.cancelSale(uuid, cancelSaleDto.userUuid);
    
    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: cancelSaleDto.userUuid,
    });
    
    // Log the sale cancellation
    await this.logsService.create({
      userUuid: cancelSaleDto.userUuid,
      operation: "cancelled",
      entity: `sale_${uuid}`,
      description: `Sale cancelled`,
      data: {
        saleUuid: uuid,
        userName: entityNames.userName,
        cancelledAt: new Date().toISOString(),
        // Include sale details from the result if available
        saleDetails: result,
      },
    });
    
    return result;
  }

  @Patch(":uuid/status")
  @ApiOperation({ summary: "Update sale status with stock management" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale status updated successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid status or userUuid" })
  async updateSaleStatus(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateSaleStatusDto: UpdateSaleStatusDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    console.log("[Sales Controller] Update sale status request:", { 
      uuid, 
      userUuid: updateSaleStatusDto.userUuid, 
      status: updateSaleStatusDto.status 
    });
    
    const result = await this.salesService.updateSaleStatus(uuid, updateSaleStatusDto.status, updateSaleStatusDto.userUuid);
    
    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: updateSaleStatusDto.userUuid,
    });
    
    // Log the sale status update
    await this.logsService.create({
      userUuid: updateSaleStatusDto.userUuid,
      operation: "status_updated",
      entity: `sale_${uuid}`,
      description: `Sale status updated to ${updateSaleStatusDto.status}`,
      data: {
        saleUuid: uuid,
        userName: entityNames.userName,
        newStatus: updateSaleStatusDto.status,
        statusUpdatedAt: new Date().toISOString(),
        // Include updated sale details from the result
        saleDetails: result,
      },
    });
    
    return result;
  }

  @Get()
  @ApiOperation({ summary: "Get all sales with pagination and filtering" })
  @ApiQuery({
    name: "customerUuid",
    required: false,
    type: String,
    description: "Filter by customer UUID (optional)",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID (optional)",
  })
  @ApiQuery({
    name: "status",
    required: false,
    type: String,
    description: "Filter by sale status (optional)",
  })
  @ApiQuery({
    name: "createdFrom",
    required: false,
    type: String,
    description: "Filter sales created after this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "createdTo",
    required: false,
    type: String,
    description: "Filter sales created before this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "invoiceNumber",
    required: false,
    type: String,
    description: "Filter by invoice number (optional, partial match)",
  })
  @ApiQuery({
    name: "paymentMethod",
    required: false,
    type: String,
    description: "Filter by payment method (optional)",
  })
  @ApiQuery({
    name: "startDate",
    required: false,
    type: String,
    description: "Filter sales with invoice date after this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "endDate",
    required: false,
    type: String,
    description: "Filter sales with invoice date before this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "minAmount",
    required: false,
    type: Number,
    description: "Filter sales with total amount greater than or equal to this value (optional)",
  })
  @ApiQuery({
    name: "maxAmount",
    required: false,
    type: Number,
    description: "Filter sales with total amount less than or equal to this value (optional)",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiResponse({
    status: 200,
    description: "Sales retrieved successfully",
    type: SalesListResponseDto,
  })
  async findAll(
    @Query("customerUuid") customerUuid?: string,
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("status") status?: string,
    @Query("createdFrom") createdFrom?: string,
    @Query("createdTo") createdTo?: string,
    @Query("invoiceNumber") invoiceNumber?: string,
    @Query("paymentMethod") paymentMethod?: string,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string,
    @Query("minAmount") minAmount?: string,
    @Query("maxAmount") maxAmount?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<SalesListResponseDto> {
    // Add 5-second delay for debugging
    await debugDelay();
    
    // Use constants for default values
    const customerUuidFilter = customerUuid ?? EMPTY_UUID_FILTER;
    const statusFilter = status ?? EMPTY_STRING_FILTER;

    // Use wide date range if not specified
    const createdFromDate = createdFrom
      ? new Date(createdFrom)
      : MIN_DATE_FILTER;
    const createdToDate = createdTo ? new Date(createdTo) : MAX_DATE_FILTER;

    // Parse date filters for invoice date range
    const startDateFilter = startDate ? new Date(startDate) : undefined;
    const endDateFilter = endDate ? new Date(endDate) : undefined;

    // Parse and validate amount filters
    const parsedMinAmount = minAmount ? parseFloat(minAmount) : undefined;
    const parsedMaxAmount = maxAmount ? parseFloat(maxAmount) : undefined;

    return this.salesService.findAll({
      customerUuid: customerUuidFilter,
      warehouseUuid,
      status: statusFilter,
      createdFrom: createdFromDate,
      createdTo: createdToDate,
      invoiceNumber,
      paymentMethod,
      startDate: startDateFilter,
      endDate: endDateFilter,
      minAmount: parsedMinAmount,
      maxAmount: parsedMaxAmount,
      page: page || 1,
      limit: limit || 10,
    });
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a sale by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale retrieved successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<SaleResponseDto> {
    // Add 5-second delay for debugging
    await debugDelay();
    
    return this.salesService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update a sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale updated successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid payment amount - cannot overpay" })
  @ApiResponse({ status: 400, description: "Invalid userUuid" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateSaleDto: UpdateSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    console.log('[Sales Controller] Update sale request received');
    console.log('[Sales Controller] Sale UUID:', uuid);
    console.log('[Sales Controller] User UUID:', updateSaleDto.userUuid);
    console.log('[Sales Controller] Update data:', JSON.stringify(updateSaleDto, null, 2));
    console.log('[Sales Controller] Update data types:', {
      userUuid: typeof updateSaleDto.userUuid,
      customerUuid: typeof updateSaleDto.customerUuid,
      paymentMethod: typeof updateSaleDto.paymentMethod,
      amountPaid: typeof updateSaleDto.amountPaid,
      useTax: typeof updateSaleDto.useTax,
      taxRate: typeof updateSaleDto.taxRate
    });
    
    // Note: Payment amount validation is handled in the service after totals are recalculated
    // This ensures validation happens against the updated total amount, not the old one
    
    // Convert date strings to Date objects if provided
    const updateData: any = { ...updateSaleDto };
    if (updateData.invoiceDate) {
      updateData.invoiceDate = new Date(updateData.invoiceDate);
    }
    if (updateData.dueDate) {
      updateData.dueDate = new Date(updateData.dueDate);
    }
    if (updateData.paymentDate && Array.isArray(updateData.paymentDate)) {
      updateData.paymentDate = updateData.paymentDate.map(dateStr => new Date(dateStr));
    }
    
    console.log('[Sales Controller] Processed update data:', JSON.stringify(updateData, null, 2));
    console.log('[Sales Controller] Calling sales service update method');
    
    try {
      const result = await this.salesService.update(uuid, updateData, updateSaleDto.userUuid);
      console.log('[Sales Controller] Sale update completed successfully');
      
      // Get entity names for logging
      const entityNames = await this.getEntityNames({
        customerUuid: updateSaleDto.customerUuid,
        userUuid: updateSaleDto.userUuid,
        productUuids: updateSaleDto.items?.map(item => item.productUuid) || [],
      });
      
      // Log the sale update
      await this.logsService.create({
        userUuid: updateSaleDto.userUuid,
        operation: "updated",
        entity: `sale_${uuid}`,
        description: `Sale updated`,
        data: {
          saleUuid: uuid,
          updatedFields: Object.keys(updateData),
          changes: updateData,
          customerUuid: updateSaleDto.customerUuid,
          customerName: entityNames.customerName,
          userName: entityNames.userName,
          paymentMethod: updateSaleDto.paymentMethod,
          amountPaid: updateSaleDto.amountPaid,
          useTax: updateSaleDto.useTax,
          taxRate: updateSaleDto.taxRate,
          // Include items if they were updated
          items: updateSaleDto.items?.map(item => ({
            productUuid: item.productUuid,
            productName: entityNames.productNames?.[item.productUuid] || item.name,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            lineTotal: item.totalPrice || item.quantity * item.unitPrice,
            taxAmount: 0, // UpdateSaleDto doesn't have taxAmount per item
          })) || undefined,
        },
      });
      
      return result;
    } catch (error) {
      console.error('[Sales Controller] Error in sale update:', error);
      console.error('[Sales Controller] Error message:', error.message);
      throw error;
    }
  }

  @Post(":uuid/add-products")
  @ApiOperation({ summary: "Add products to an existing sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Products added to sale successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  async addProductsToSale(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() addProductsDto: AddProductsToSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    console.log("[Sales Controller] Add products to sale request:", { 
      uuid, 
      userUuid: addProductsDto.userUuid,
      itemsCount: addProductsDto.items?.length || 0 
    });
    
    const result = await this.salesService.addProducts(uuid, addProductsDto.items);
    
    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: addProductsDto.userUuid,
      productUuids: addProductsDto.items?.map(item => item.productUuid) || [],
    });
    
    // Log the products addition
    await this.logsService.create({
      userUuid: addProductsDto.userUuid,
      operation: "products_added",
      entity: `sale_${uuid}`,
      description: `Added ${addProductsDto.items?.length || 0} products to sale`,
      data: {
        saleUuid: uuid,
        userName: entityNames.userName,
        itemsAdded: addProductsDto.items?.map(item => ({
          productUuid: item.productUuid,
          productName: entityNames.productNames?.[item.productUuid] || item.name,
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          lineTotal: item.lineTotal || item.quantity * item.unitPrice,
          taxAmount: item.taxAmount || 0,
        })) || [],
        itemsCount: addProductsDto.items?.length || 0,
        addedAt: new Date().toISOString(),
        // Include updated sale details from the result
        saleDetails: result,
      },
    });
    
    return result;
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete a sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({ status: 200, description: "Sale deleted successfully" })
  @ApiResponse({ status: 404, description: "Sale not found" })
  async remove(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() deleteSaleDto: DeleteSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    // Get sale details before deletion for logging
    let saleDetails = null;
    try {
      saleDetails = await this.salesService.findOne(uuid);
    } catch (error) {
      console.log("[Sales Controller] Could not retrieve sale details for logging:", error.message);
    }
    
    const result = await this.salesService.remove(uuid);
    
    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: deleteSaleDto.userUuid,
    });
    
    // Log the sale deletion
    await this.logsService.create({
      userUuid: deleteSaleDto.userUuid,
      operation: "deleted",
      entity: `sale_${uuid}`,
      description: `Sale deleted`,
      data: {
        saleUuid: uuid,
        userName: entityNames.userName,
        deletedAt: new Date().toISOString(),
        // Include sale details before deletion
        saleDetails: saleDetails,
      },
    });
    
    return result;
  }

  @Get("list-by-warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Get all sales for a specific warehouse" })
  @ApiParam({
    name: "warehouseUuid",
    type: String,
    example: "uuid-v7-string",
    description: "Warehouse UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sales retrieved successfully",
    type: [SaleResponseDto],
  })
  async listByWarehouse(@Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    return this.salesService.findByWarehouse(warehouseUuid);
  }

  @Post("fix-data-inconsistencies")
  @ApiOperation({ summary: "Fix data inconsistencies in sales (admin only)" })
  @ApiResponse({
    status: 200,
    description: "Data inconsistencies fixed",
    schema: {
      type: "object",
      properties: {
        fixed: { type: "number", description: "Number of sales fixed" },
        total: { type: "number", description: "Total number of sales processed" },
        message: { type: "string", description: "Result message" }
      }
    }
  })
  async fixDataInconsistencies() {
    // Add 5-second delay for debugging
    await debugDelay();
    
    const result = await this.salesService.fixSalesDataInconsistencies();
    
    return {
      ...result,
      message: `Fixed ${result.fixed} out of ${result.total} sales with data inconsistencies`
    };
  }

  @Get(":uuid/debug-items")
  @ApiOperation({ summary: "Debug sale items data" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale items debug information",
  })
  async debugSaleItems(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    return this.salesService.debugSaleItems(uuid);
  }

  @Post(":uuid/fix-line-totals")
  @ApiOperation({ summary: "Fix sale items with string lineTotal values" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale items fixed",
  })
  async fixSaleItemsLineTotal(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    return this.salesService.fixSaleItemsLineTotal(uuid);
  }


}
